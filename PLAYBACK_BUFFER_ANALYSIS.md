# Playback Buffer 填充逻辑分析 (更新版)

## 新功能：支持指定播放通道

现在支持将采集的任意2个通道输出到播放设备的任意2个通道，不再限制只能输出到前2个播放通道。

## 当前实现概述

支持灵活的通道映射，例如32通道采集 -> 8通道播放的场景：

## 数据流程

### 1. 缓冲区分配
```c
// 分配缓冲区
int32_t *capture_buffer = malloc(frames_to_process * capture_dev.channels * SAMPLE_SIZE);
int32_t *playback_buffer = malloc(frames_to_process * playback_dev.channels * SAMPLE_SIZE);

// 对于32ch capture, 2ch playback:
// capture_buffer: frames_to_process * 32 * 4 bytes
// playback_buffer: frames_to_process * 2 * 4 bytes
```

### 2. 通道选择参数
```c
static int ch1 = 0, ch2 = 1;   // 默认选择采集通道0和1
static int pch1 = 0, pch2 = 1; // 默认输出到播放通道0和1
// 可通过命令行参数修改:
// -c, -C: 选择采集通道
// -p, -P: 选择播放通道
```

### 3. 新的数据填充逻辑
```c
// 提取选定的通道数据并检查信号强度
int32_t max_sample = 0;

// 首先将所有播放通道填充0
for (snd_pcm_uframes_t i = 0; i < frames_read; i++) {
    for (int j = 0; j < playback_dev.channels; j++) {
        playback_buffer[i * playback_dev.channels + j] = 0;
    }
}

// 然后填充指定的播放通道
for (snd_pcm_uframes_t i = 0; i < frames_read; i++) {
    // 处理第一个通道映射: capture ch1 -> playback pch1
    if (ch1 < capture_dev.channels && pch1 < playback_dev.channels) {
        int32_t sample = capture_buffer[i * capture_dev.channels + ch1];
        playback_buffer[i * playback_dev.channels + pch1] = sample;

        // 计算最大采样值用于调试
        if (abs(sample) > abs(max_sample)) {
            max_sample = sample;
        }
    }

    // 处理第二个通道映射: capture ch2 -> playback pch2
    if (ch2 < capture_dev.channels && pch2 < playback_dev.channels) {
        int32_t sample = capture_buffer[i * capture_dev.channels + ch2];
        playback_buffer[i * playback_dev.channels + pch2] = sample;

        // 计算最大采样值用于调试
        if (abs(sample) > abs(max_sample)) {
            max_sample = sample;
        }
    }
}
```

## 具体示例：32ch -> 8ch

### 默认配置 (ch1=0, ch2=1, pch1=0, pch2=1)
- **Playback 通道0**: 来自 Capture 通道0
- **Playback 通道1**: 来自 Capture 通道1
- **Playback 通道2-7**: 静音 (填充0)

### 自定义配置 (例如 -c 5 -C 7 -p 2 -P 3)
- **Playback 通道0-1**: 静音 (填充0)
- **Playback 通道2**: 来自 Capture 通道5
- **Playback 通道3**: 来自 Capture 通道7
- **Playback 通道4-7**: 静音 (填充0)

### 高级配置 (例如 -c 10 -C 15 -p 4 -P 6)
- **Playback 通道0-3**: 静音 (填充0)
- **Playback 通道4**: 来自 Capture 通道10
- **Playback 通道5**: 静音 (填充0)
- **Playback 通道6**: 来自 Capture 通道15
- **Playback 通道7**: 静音 (填充0)

## 内存布局

### Capture Buffer (32通道交错)
```
Frame 0: [Ch0][Ch1][Ch2]...[Ch31]
Frame 1: [Ch0][Ch1][Ch2]...[Ch31]
Frame 2: [Ch0][Ch1][Ch2]...[Ch31]
...
```

### Playback Buffer (8通道交错，示例配置 ch1=5, ch2=7, pch1=2, pch2=3)
```
Frame 0: [0][0][Ch5][Ch7][0][0][0][0]
Frame 1: [0][0][Ch5][Ch7][0][0][0][0]
Frame 2: [0][0][Ch5][Ch7][0][0][0][0]
...
```

## 数组索引计算

### Capture Buffer 索引
```c
// 第i帧，第ch1通道的数据
int capture_index1 = i * capture_dev.channels + ch1;
int32_t sample1 = capture_buffer[capture_index1];

// 第i帧，第ch2通道的数据
int capture_index2 = i * capture_dev.channels + ch2;
int32_t sample2 = capture_buffer[capture_index2];
```

### Playback Buffer 索引
```c
// 第i帧，第pch1通道的数据
int playback_index1 = i * playback_dev.channels + pch1;
playback_buffer[playback_index1] = sample1;

// 第i帧，第pch2通道的数据
int playback_index2 = i * playback_dev.channels + pch2;
playback_buffer[playback_index2] = sample2;
```

## 当前实现的特点

### 优点
1. **完全灵活的通道映射**: 可以选择任意采集通道输出到任意播放通道
2. **支持多通道播放设备**: 不再限制只能输出到前2个播放通道
3. **简单直接**: 直接复制采样数据，无额外处理
4. **自动静音**: 未映射的播放通道自动填充0

### 限制
1. **固定2通道映射**: 只支持映射2个采集通道
2. **无混音功能**: 不支持多通道混音
3. **无音量控制**: 直接复制原始采样值

## 可能的改进方向

1. **支持单声道输出**: 将选定通道复制到所有播放通道
2. **支持混音**: 将多个采集通道混音到立体声
3. **音量控制**: 添加增益调节功能
4. **通道映射**: 支持更复杂的通道路由配置

## 使用示例

```bash
# 默认：采集通道0->播放通道0，采集通道1->播放通道1
./alsa_c2p_test

# 自定义采集通道：采集通道5->播放通道0，采集通道7->播放通道1
./alsa_c2p_test -c 5 -C 7

# 自定义播放通道：采集通道0->播放通道2，采集通道1->播放通道3
./alsa_c2p_test -p 2 -P 3

# 完全自定义：采集通道5,7->播放通道2,3
./alsa_c2p_test -c 5 -C 7 -p 2 -P 3

# 8通道播放设备，输出到后面的通道：采集0,1->播放4,5
./alsa_c2p_test -O 8 -c 0 -C 1 -p 4 -P 5

# 单声道到多个播放通道：采集通道3->播放通道0和2
./alsa_c2p_test -c 3 -C 3 -p 0 -P 2

# 交叉映射：采集0->播放1，采集1->播放0 (左右声道交换)
./alsa_c2p_test -c 0 -C 1 -p 1 -P 0
```
