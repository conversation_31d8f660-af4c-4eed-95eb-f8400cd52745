# ALSA音频采集播放测试程序

## 概述
这是一个基于ALSA的音频采集播放测试程序，可以从指定的采集设备读取音频数据，选择特定通道，然后输出到指定的播放设备。

## 编译
```bash
gcc -o alsa_c2p_test alsa_c2p_test.c -lasound -lpthread
```

## 使用方法

### 基本语法
```bash
./alsa_c2p_test [选项]
```

### 命令行参数

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| -i | --input | 指定采集设备 | hw:0,0 |
| -o | --output | 指定播放设备 | hw:2,0 |
| -I | --input-ch | 指定采集通道数 | 32 |
| -O | --output-ch | 指定播放通道数 | 2 |
| -c | --ch1 | 选择第一个通道 | 0 |
| -C | --ch2 | 选择第二个通道 | 1 |
| -f | --frame | 设置帧长(1,4,8ms) | 4 |
| -d | --debug | 启用调试模式 | 关闭 |
| -h | --help | 显示帮助信息 | - |

### 使用示例

1. **使用默认设置**
   ```bash
   ./alsa_c2p_test
   ```

2. **指定采集和播放设备**
   ```bash
   ./alsa_c2p_test -i hw:1,0 -o hw:3,0
   ```

3. **设置通道配置**
   ```bash
   ./alsa_c2p_test -I 8 -O 4 -c 2 -C 3
   ```
   - 采集设备使用8通道
   - 播放设备使用4通道
   - 选择采集通道2和3进行播放

4. **调整帧长和启用调试**
   ```bash
   ./alsa_c2p_test -c 2 -C 3 -f 8 -d
   ```

5. **完整配置示例**
   ```bash
   ./alsa_c2p_test -i hw:1,0 -o hw:2,0 -I 16 -O 2 -c 4 -C 5 -f 4 -d
   ```

## 功能特性

- **灵活的设备配置**: 可以指定任意的ALSA采集和播放设备
- **可配置通道数**: 支持1-64个采集通道，1-32个播放通道
- **通道选择**: 可以选择采集设备的任意两个通道进行播放
- **实时处理**: 支持1ms、4ms、8ms的帧长设置
- **错误恢复**: 自动处理缓冲区溢出/下溢等错误
- **调试模式**: 提供详细的运行信息

## 注意事项

1. **权限要求**: 程序会尝试设置实时优先级，需要root权限或适当的权限配置
2. **设备存在性**: 确保指定的音频设备存在且可访问
3. **通道范围**: 选择的通道号必须在采集设备支持的范围内
4. **系统负载**: 实时音频处理对系统性能有一定要求

## 错误处理

程序会自动处理以下错误情况：
- 音频设备打开失败
- 缓冲区溢出/下溢
- 参数范围错误
- 设备配置错误

## 退出程序

使用 Ctrl+C 或发送 SIGTERM 信号来安全退出程序。
