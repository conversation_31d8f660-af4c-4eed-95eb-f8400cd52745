#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <sched.h>
#include <alsa/asoundlib.h>

// 音频参数定义
#define SAMPLE_RATE     48000
#define DEFAULT_CAPTURE_CHANNELS 32
#define DEFAULT_PLAYBACK_CHANNELS 2
#define FORMAT          SND_PCM_FORMAT_S32_LE
#define SAMPLE_SIZE     4  // S32_LE每样本4字节

// 音频设备状态枚举
typedef enum {
    DEVICE_STATE_CLOSED = 0,
    DEVICE_STATE_OPENED,
    DEVICE_STATE_PREPARED,
    DEVICE_STATE_RUNNING,
    DEVICE_STATE_ERROR
} device_state_t;

// 音频设备结构体
typedef struct {
    snd_pcm_t *handle;
    char device_name[64];
    int channels;
    snd_pcm_uframes_t period_size;
    snd_pcm_uframes_t buffer_size;
    device_state_t state;
    int underrun_count;
} audio_device_t;

// 全局变量
static int running = 1;
static audio_device_t capture_dev = {0};
static audio_device_t playback_dev = {0};
static int ch1 = 0, ch2 = 1;  // 默认选择通道0和1
static int frame_time = 4;    // 默认4ms帧长
static int debug_mode = 0;    // 调试模式
static int use_mmap = 0;      // 是否使用mmap模式
static char capture_device[64] = "hw:0,0";  // 默认采集设备
static char playback_device[64] = "hw:2,0"; // 默认播放设备
static int capture_channels = DEFAULT_CAPTURE_CHANNELS;  // 采集通道数
static int playback_channels = DEFAULT_PLAYBACK_CHANNELS; // 播放通道数

// 信号处理函数
void signal_handler(int sig) {
    printf("\n接收到信号 %d, 正在退出...\n", sig);
    running = 0;
}

// 设置进程优先级
int set_high_priority() {
    struct sched_param param;
    // 在MMAP模式下使用较低的实时优先级，避免RT throttling
    param.sched_priority = use_mmap ? 50 : 90;

    if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
        perror("设置实时优先级失败");
        return -1;
    }

    printf("进程优先级已设置为实时模式，优先级: %d\n", param.sched_priority);
    return 0;
}

// 通用设备初始化函数
int init_audio_device(audio_device_t *dev, const char *device_name,
                     snd_pcm_stream_t stream, int channels) {
    int err;
    snd_pcm_hw_params_t *hw_params;

    // 初始化设备结构体
    strncpy(dev->device_name, device_name, sizeof(dev->device_name) - 1);
    dev->device_name[sizeof(dev->device_name) - 1] = '\0';
    dev->channels = channels;
    dev->state = DEVICE_STATE_CLOSED;
    dev->underrun_count = 0;

    // 打开PCM设备 - 使用阻塞模式确保稳定性
    if ((err = snd_pcm_open(&dev->handle, device_name, stream, 0)) < 0) {
        fprintf(stderr, "无法打开设备 %s: %s\n", device_name, snd_strerror(err));
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }
    dev->state = DEVICE_STATE_OPENED;

    // 分配硬件参数结构体
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构体: %s\n", snd_strerror(err));
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(dev->handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置访问模式 - 根据是否使用mmap选择不同模式
    snd_pcm_access_t access_type = use_mmap ? SND_PCM_ACCESS_MMAP_INTERLEAVED : SND_PCM_ACCESS_RW_INTERLEAVED;
    if ((err = snd_pcm_hw_params_set_access(dev->handle, hw_params, access_type)) < 0) {
        fprintf(stderr, "无法设置访问模式: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(dev->handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置采样率
    unsigned int rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(dev->handle, hw_params, &rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置通道数
    if ((err = snd_pcm_hw_params_set_channels(dev->handle, hw_params, channels)) < 0) {
        fprintf(stderr, "无法设置通道数: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置周期大小（帧数）
    snd_pcm_uframes_t frames = (SAMPLE_RATE * frame_time) / 1000;
    if ((err = snd_pcm_hw_params_set_period_size_near(dev->handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 设置缓冲区大小
    snd_pcm_uframes_t buffer_size = frames * 4;  // 增加缓冲区大小以提高稳定性
    if ((err = snd_pcm_hw_params_set_buffer_size_near(dev->handle, hw_params, &buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 应用硬件参数
    if ((err = snd_pcm_hw_params(dev->handle, hw_params)) < 0) {
        fprintf(stderr, "无法应用硬件参数: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    // 获取实际设置的参数
    snd_pcm_hw_params_get_period_size(hw_params, &dev->period_size, NULL);
    snd_pcm_hw_params_get_buffer_size(hw_params, &dev->buffer_size);

    snd_pcm_hw_params_free(hw_params);

    // 准备PCM设备
    if ((err = snd_pcm_prepare(dev->handle)) < 0) {
        fprintf(stderr, "无法准备PCM设备: %s\n", snd_strerror(err));
        snd_pcm_close(dev->handle);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }
    dev->state = DEVICE_STATE_PREPARED;

    printf("设备初始化成功: %s, %d通道, %dHz, 周期大小:%lu, 缓冲区大小:%lu\n",
           device_name, channels, SAMPLE_RATE, dev->period_size, dev->buffer_size);

    return 0;
}

// 初始化采集设备的包装函数
int init_capture_device() {
    return init_audio_device(&capture_dev, capture_device, SND_PCM_STREAM_CAPTURE, capture_channels);
}

// 初始化播放设备的包装函数
int init_playback_device() {
    return init_audio_device(&playback_dev, playback_device, SND_PCM_STREAM_PLAYBACK, playback_channels);
}

// 启动设备
int start_device(audio_device_t *dev) {
    int err;

    if (dev->state != DEVICE_STATE_PREPARED && dev->state != DEVICE_STATE_RUNNING) {
        fprintf(stderr, "设备状态错误，无法启动\n");
        return -1;
    }

    if (dev->state == DEVICE_STATE_RUNNING) {
        return 0; // 已经在运行
    }

    if ((err = snd_pcm_start(dev->handle)) < 0) {
        fprintf(stderr, "无法启动设备 %s: %s\n", dev->device_name, snd_strerror(err));
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }

    dev->state = DEVICE_STATE_RUNNING;
    printf("设备 %s 已启动\n", dev->device_name);
    return 0;
}

// 恢复设备
int recover_device(audio_device_t *dev) {
    int err;
    printf("设备 %s 缓冲区异常，恢复中...\n", dev->device_name);

    if ((err = snd_pcm_prepare(dev->handle)) < 0) {
        fprintf(stderr, "无法准备设备 %s: %s\n", dev->device_name, snd_strerror(err));
        dev->state = DEVICE_STATE_ERROR;
        return err;
    }

    dev->state = DEVICE_STATE_PREPARED;

    // 对于播放设备，需要重新启动
    if ((err = snd_pcm_start(dev->handle)) < 0) {
        fprintf(stderr, "无法启动设备 %s: %s\n", dev->device_name, snd_strerror(err));
        dev->state = DEVICE_STATE_ERROR;
        return err;
    }

    dev->state = DEVICE_STATE_RUNNING;
    dev->underrun_count++;

    return 0;
}

// MMAP方式读取音频数据 - 修复版本
int mmap_read_audio(snd_pcm_t *handle, int32_t *buffer, snd_pcm_uframes_t frames, int channels) {
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_available;
    int err;

    frames_available = frames;

    err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_available);
    if (err < 0) {
        if (err == -EPIPE) {
            printf("采集设备缓冲区溢出，恢复中...\n");
            snd_pcm_prepare(handle);
            return err;
        }
        fprintf(stderr, "mmap_begin错误: %s\n", snd_strerror(err));
        return err;
    }

    // 检查是否有可用数据
    if (frames_available == 0) {
        snd_pcm_mmap_commit(handle, offset, 0);
        return 0;
    }

    // 只处理实际可用的帧数
    snd_pcm_uframes_t frames_to_copy = frames_available < frames ? frames_available : frames;

    if (debug_mode && frames_to_copy > 0) {
        printf("MMAP读取: offset=%lu, 可用帧=%lu, 拷贝帧=%lu, 通道=%d\n",
               offset, frames_available, frames_to_copy, channels);
    }

    // 修复：正确的MMAP数据拷贝，处理交错格式
    if (areas[0].step == 32 * channels) {
        // 标准交错格式 - 所有通道数据紧密排列
        int32_t *src = (int32_t *)((char *)areas[0].addr + (areas[0].first / 8) + offset * (areas[0].step / 8));
        
        for (snd_pcm_uframes_t i = 0; i < frames_to_copy; i++) {
            for (int ch = 0; ch < channels; ch++) {
                buffer[i * channels + ch] = src[i * channels + ch];
            }
        }
    } else {
        // 非标准格式，按通道单独处理
        for (int ch = 0; ch < channels; ch++) {
            char *channel_addr = (char *)areas[ch].addr;
            unsigned int first_bit = areas[ch].first;
            unsigned int step_bits = areas[ch].step;
            
            char *start_addr = channel_addr + (first_bit + offset * step_bits) / 8;
            
            for (snd_pcm_uframes_t i = 0; i < frames_to_copy; i++) {
                int32_t *sample_addr = (int32_t *)(start_addr + (i * step_bits) / 8);
                buffer[i * channels + ch] = *sample_addr;
            }
        }
    }

    err = snd_pcm_mmap_commit(handle, offset, frames_to_copy);
    if (err < 0 || (snd_pcm_uframes_t)err != frames_to_copy) {
        fprintf(stderr, "mmap_commit错误: %s\n", snd_strerror(err));
        return err < 0 ? err : -EIO;
    }

    return frames_to_copy;
}

// MMAP方式写入音频数据 - 修复版本
int mmap_write_audio(snd_pcm_t *handle, int32_t *buffer, snd_pcm_uframes_t frames, int channels) {
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_available;
    int err;

    frames_available = frames;

    err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_available);
    if (err < 0) {
        if (err == -EPIPE) {
            printf("播放设备缓冲区下溢，恢复中...\n");
            if (recover_device(&playback_dev) < 0) {
                return err;
            }
            return -EPIPE;
        }
        fprintf(stderr, "mmap_begin错误: %s\n", snd_strerror(err));
        return err;
    }

    // 检查是否有可用空间
    if (frames_available == 0) {
        if (debug_mode) {
            printf("MMAP写入: offset=%lu, 可用帧=%lu, 没有可用空间\n", offset, frames_available);
        }
        snd_pcm_mmap_commit(handle, offset, 0);
        return 0;
    }

    // 只处理实际可用的帧数
    snd_pcm_uframes_t frames_to_copy = frames_available < frames ? frames_available : frames;

    if (debug_mode && frames_to_copy > 0) {
        printf("MMAP写入: offset=%lu, 可用帧=%lu, 拷贝帧=%lu, 通道=%d\n",
               offset, frames_available, frames_to_copy, channels);
    }

    // 修复：正确的MMAP数据写入，处理交错格式
    if (areas[0].step == 32 * channels) {
        // 标准交错格式 - 所有通道数据紧密排列
        int32_t *dst = (int32_t *)((char *)areas[0].addr + (areas[0].first / 8) + offset * (areas[0].step / 8));
        
        for (snd_pcm_uframes_t i = 0; i < frames_to_copy; i++) {
            for (int ch = 0; ch < channels; ch++) {
                dst[i * channels + ch] = buffer[i * channels + ch];
            }
        }
    } else {
        // 非标准格式，按通道单独处理
        for (int ch = 0; ch < channels; ch++) {
            char *channel_addr = (char *)areas[ch].addr;
            unsigned int first_bit = areas[ch].first;
            unsigned int step_bits = areas[ch].step;
            
            char *start_addr = channel_addr + (first_bit + offset * step_bits) / 8;
            
            for (snd_pcm_uframes_t i = 0; i < frames_to_copy; i++) {
                int32_t *sample_addr = (int32_t *)(start_addr + (i * step_bits) / 8);
                *sample_addr = buffer[i * channels + ch];
            }
        }
    }

    err = snd_pcm_mmap_commit(handle, offset, frames_to_copy);
    if (err < 0 || (snd_pcm_uframes_t)err != frames_to_copy) {
        fprintf(stderr, "mmap_commit错误: %s\n", snd_strerror(err));
        return err < 0 ? err : -EIO;
    }

    return frames_to_copy;
}

// 音频处理主循环
void audio_loop() {
    int err;
    snd_pcm_uframes_t frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
    snd_pcm_uframes_t total_frames_processed = 0;

    // 分配缓冲区
    int32_t *capture_buffer = malloc(frames_to_process * capture_dev.channels * SAMPLE_SIZE);
    int32_t *playback_buffer = malloc(frames_to_process * playback_dev.channels * SAMPLE_SIZE);

    if (!capture_buffer || !playback_buffer) {
        fprintf(stderr, "无法分配音频缓冲区\n");
        return;
    }

    // 预填充播放缓冲区，避免开始时的下溢
    memset(playback_buffer, 0, frames_to_process * playback_dev.channels * SAMPLE_SIZE);
    int prefill_periods = 2;  // 预填充2个周期
    for (int i = 0; i < prefill_periods; i++) {
        if (use_mmap) {
            mmap_write_audio(playback_dev.handle, playback_buffer, frames_to_process, playback_dev.channels);
        } else {
            snd_pcm_writei(playback_dev.handle, playback_buffer, frames_to_process);
        }
    }

    // 启动播放设备 - 修复：确保在mmap模式下也正确启动
    if (start_device(&playback_dev) < 0) {
        fprintf(stderr, "无法启动播放设备\n");
        free(capture_buffer);
        free(playback_buffer);
        return;
    }

    // 启动采集设备
    if (start_device(&capture_dev) < 0) {
        fprintf(stderr, "无法启动采集设备\n");
        free(capture_buffer);
        free(playback_buffer);
        return;
    }

    printf("开始音频处理循环，选择通道: %d, %d\n", ch1, ch2);
    printf("按 Ctrl+C 停止程序\n");
    
    while (running) {
        // 等待采集设备有数据可用
        if (use_mmap) {
            snd_pcm_sframes_t avail = snd_pcm_avail(capture_dev.handle);
            if (avail < (snd_pcm_sframes_t)frames_to_process) {
                if (avail < 0) {
                    if (avail == -EPIPE) {
                        printf("采集设备缓冲区溢出，恢复中...\n");
                        recover_device(&capture_dev);
                    }
                    continue;
                }
                usleep(100); // 等待更多数据
                continue;
            }
        }

        // 从采集设备读取数据
        if (use_mmap) {
            err = mmap_read_audio(capture_dev.handle, capture_buffer, frames_to_process, capture_dev.channels);
        } else {
            err = snd_pcm_readi(capture_dev.handle, capture_buffer, frames_to_process);
        }

        if (err == -EAGAIN) {
            usleep(1000);
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                printf("采集设备缓冲区溢出，恢复中...\n");
                recover_device(&capture_dev);
                usleep(1000);
            } else {
                fprintf(stderr, "读取采集数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }

        if (err == 0) {
            usleep(100);
            continue;
        }

        // 使用实际读取的帧数
        snd_pcm_uframes_t frames_read = (snd_pcm_uframes_t)err;
        total_frames_processed += frames_read;

        // 提取选定的通道数据并检查信号强度
        int32_t max_sample = 0;
        for (snd_pcm_uframes_t i = 0; i < frames_read; i++) {
            for (int j = 0; j < playback_dev.channels && j < 2; j++) {
                int src_ch = (j == 0) ? ch1 : ch2;
                if (src_ch < capture_dev.channels) {
                    int32_t sample = capture_buffer[i * capture_dev.channels + src_ch];
                    playback_buffer[i * playback_dev.channels + j] = sample;

                    // 计算最大采样值用于调试
                    if (abs(sample) > abs(max_sample)) {
                        max_sample = sample;
                    }
                } else {
                    playback_buffer[i * playback_dev.channels + j] = 0;
                }
            }
            // 其余播放通道填充0
            for (int j = 2; j < playback_dev.channels; j++) {
                playback_buffer[i * playback_dev.channels + j] = 0;
            }
        }
        
        // 调试信息：显示信号强度
        if (debug_mode && total_frames_processed % (SAMPLE_RATE/4) == 0) { // 每0.25秒显示一次
            printf("处理 %lu 帧, 最大采样值: %d (0x%08X)\n", frames_read, max_sample, max_sample);
        }
        
        // 等待播放设备有空间可用
        if (use_mmap) {
            snd_pcm_sframes_t avail = snd_pcm_avail(playback_dev.handle);
            if (avail < (snd_pcm_sframes_t)frames_read) {
                if (avail < 0) {
                    if (avail == -EPIPE) {
                        if (recover_device(&playback_dev) < 0) {
                            break;
                        }
                    }
                    continue;
                }
                // 播放缓冲区满，等待
                usleep(100);
                continue;
            }
        }

        // 向播放设备写入数据
        if (use_mmap) {
            err = mmap_write_audio(playback_dev.handle, playback_buffer, frames_read, playback_dev.channels);
        } else {
            err = snd_pcm_writei(playback_dev.handle, playback_buffer, frames_read);
        }

        if (err == -EAGAIN) {
            usleep(1000);
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                if (playback_dev.underrun_count > 10) {
                    fprintf(stderr, "播放设备频繁下溢，可能存在系统问题\n");
                    break;
                }
                if (recover_device(&playback_dev) < 0) {
                    break;
                }
                usleep(1000);
            } else {
                fprintf(stderr, "写入播放数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }

        // 重置下溢计数器
        if (playback_dev.underrun_count > 0) {
            playback_dev.underrun_count = 0;
        }

        // 在MMAP模式下适当让出CPU
        if (use_mmap) {
            sched_yield(); // 让出CPU时间片
        }
    }
    
    free(capture_buffer);
    free(playback_buffer);
}

// 清理设备资源
void cleanup_device(audio_device_t *dev) {
    if (dev->handle) {
        snd_pcm_close(dev->handle);
        dev->handle = NULL;
        dev->state = DEVICE_STATE_CLOSED;
        printf("设备 %s 已关闭\n", dev->device_name);
    }
}

// 清理资源
void cleanup() {
    cleanup_device(&capture_dev);
    cleanup_device(&playback_dev);
    printf("资源清理完成\n");
}

// 显示帮助信息
void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("选项:\n");
    printf("  -i, --input <dev>    指定采集设备 (默认: hw:0,0)\n");
    printf("  -o, --output <dev>   指定播放设备 (默认: hw:2,0)\n");
    printf("  -I, --input-ch <n>   指定采集通道数 (默认: %d)\n", DEFAULT_CAPTURE_CHANNELS);
    printf("  -O, --output-ch <n>  指定播放通道数 (默认: %d)\n", DEFAULT_PLAYBACK_CHANNELS);
    printf("  -c, --ch1 <n>        选择第一个通道 (默认: 0)\n");
    printf("  -C, --ch2 <n>        选择第二个通道 (默认: 1)\n");
    printf("  -f, --frame <ms>     设置帧长 (1,4,8ms, 默认: 4)\n");
    printf("  -m, --mmap           使用MMAP模式 (零拷贝，更低延迟)\n");
    printf("  -d, --debug          启用调试模式\n");
    printf("  -h, --help           显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                           使用默认设置\n", prog_name);
    printf("  %s -i hw:1,0 -o hw:3,0       指定采集和播放设备\n", prog_name);
    printf("  %s -I 8 -O 4 -c 2 -C 3       8通道采集，4通道播放，选择通道2,3\n", prog_name);
    printf("  %s -c 2 -C 3 -f 1 -m         选择通道2,3，1ms帧长，MMAP模式\n", prog_name);
    printf("  %s -m -d                     使用MMAP模式，启用调试\n", prog_name);
}

int main(int argc, char *argv[]) {
    int opt;
    struct option long_options[] = {
        {"input", required_argument, 0, 'i'},
        {"output", required_argument, 0, 'o'},
        {"input-ch", required_argument, 0, 'I'},
        {"output-ch", required_argument, 0, 'O'},
        {"ch1", required_argument, 0, 'c'},
        {"ch2", required_argument, 0, 'C'},
        {"frame", required_argument, 0, 'f'},
        {"mmap", no_argument, 0, 'm'},
        {"debug", no_argument, 0, 'd'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };

    // 解析命令行参数
    while ((opt = getopt_long(argc, argv, "i:o:I:O:c:C:f:mdh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'i':
                strncpy(capture_device, optarg, sizeof(capture_device) - 1);
                capture_device[sizeof(capture_device) - 1] = '\0';
                break;
            case 'o':
                strncpy(playback_device, optarg, sizeof(playback_device) - 1);
                playback_device[sizeof(playback_device) - 1] = '\0';
                break;
            case 'I':
                capture_channels = atoi(optarg);
                if (capture_channels <= 0 || capture_channels > 64) {
                    fprintf(stderr, "错误: 采集通道数必须在1-64之间\n");
                    return 1;
                }
                break;
            case 'O':
                playback_channels = atoi(optarg);
                if (playback_channels <= 0 || playback_channels > 32) {
                    fprintf(stderr, "错误: 播放通道数必须在1-32之间\n");
                    return 1;
                }
                break;
            case 'c':
                ch1 = atoi(optarg);
                break;
            case 'C':
                ch2 = atoi(optarg);
                break;
            case 'f':
                frame_time = atoi(optarg);
                if (frame_time != 1 && frame_time != 4 && frame_time != 8) {
                    fprintf(stderr, "错误: 帧长必须是 1, 4, 或 8ms\n");
                    return 1;
                }
                break;
            case 'm':
                use_mmap = 1;
                printf("MMAP模式已启用\n");
                break;
            case 'd':
                debug_mode = 1;
                printf("调试模式已启用\n");
                break;
            case 'h':
                show_usage(argv[0]);
                return 0;
            default:
                show_usage(argv[0]);
                return 1;
        }
    }

    // 验证通道参数
    if (ch1 < 0 || ch1 >= capture_channels) {
        fprintf(stderr, "错误: 通道1超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }
    if (ch2 < 0 || ch2 >= capture_channels) {
        fprintf(stderr, "错误: 通道2超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }
    
    printf("=== 嵌入式Linux音频测试程序 ===\n");
    printf("配置: 采集通道%d,%d -> 播放, 帧长%dms%s\n",
           ch1, ch2, frame_time,
           use_mmap ? " (MMAP模式)" : "");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 设置高优先级（需要root权限）
    if (set_high_priority() != 0) {
        printf("警告: 无法设置高优先级，继续运行...\n");
    }
    
    // 初始化ALSA设备
    if (init_capture_device() != 0) {
        cleanup();
        return 1;
    }
    
    if (init_playback_device() != 0) {
        cleanup();
        return 1;
    }
    
    // 开始音频处理
    audio_loop();
    
    // 清理资源
    cleanup();
    
    printf("程序正常退出\n");
    return 0;
}