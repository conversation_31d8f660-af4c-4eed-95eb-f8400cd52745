# Playback Buffer 填充逻辑分析

## 当前实现概述

在32通道采集、2通道播放的场景下，当前代码的填充逻辑如下：

## 数据流程

### 1. 缓冲区分配
```c
// 分配缓冲区
int32_t *capture_buffer = malloc(frames_to_process * capture_dev.channels * SAMPLE_SIZE);
int32_t *playback_buffer = malloc(frames_to_process * playback_dev.channels * SAMPLE_SIZE);

// 对于32ch capture, 2ch playback:
// capture_buffer: frames_to_process * 32 * 4 bytes
// playback_buffer: frames_to_process * 2 * 4 bytes
```

### 2. 通道选择参数
```c
static int ch1 = 0, ch2 = 1;  // 默认选择通道0和1
// 可通过命令行参数 -c 和 -C 修改
```

### 3. 数据填充逻辑
```c
// 提取选定的通道数据并检查信号强度
int32_t max_sample = 0;
for (snd_pcm_uframes_t i = 0; i < frames_read; i++) {
    // 只处理前2个播放通道
    for (int j = 0; j < playback_dev.channels && j < 2; j++) {
        int src_ch = (j == 0) ? ch1 : ch2;  // j=0时用ch1, j=1时用ch2
        if (src_ch < capture_dev.channels) {
            // 从capture buffer的指定通道复制数据到playback buffer
            int32_t sample = capture_buffer[i * capture_dev.channels + src_ch];
            playback_buffer[i * playback_dev.channels + j] = sample;
            
            // 计算最大采样值用于调试
            if (abs(sample) > abs(max_sample)) {
                max_sample = sample;
            }
        } else {
            // 如果源通道超出范围，填充0
            playback_buffer[i * playback_dev.channels + j] = 0;
        }
    }
    // 其余播放通道填充0
    for (int j = 2; j < playback_dev.channels; j++) {
        playback_buffer[i * playback_dev.channels + j] = 0;
    }
}
```

## 具体示例：32ch -> 2ch

### 默认配置 (ch1=0, ch2=1)
- **Playback Left (通道0)**: 来自 Capture 通道0
- **Playback Right (通道1)**: 来自 Capture 通道1

### 自定义配置 (例如 -c 5 -C 7)
- **Playback Left (通道0)**: 来自 Capture 通道5
- **Playback Right (通道1)**: 来自 Capture 通道7

## 内存布局

### Capture Buffer (32通道交错)
```
Frame 0: [Ch0][Ch1][Ch2]...[Ch31]
Frame 1: [Ch0][Ch1][Ch2]...[Ch31]
Frame 2: [Ch0][Ch1][Ch2]...[Ch31]
...
```

### Playback Buffer (2通道交错)
```
Frame 0: [Left][Right]  <- 来自 [Ch_ch1][Ch_ch2]
Frame 1: [Left][Right]  <- 来自 [Ch_ch1][Ch_ch2]
Frame 2: [Left][Right]  <- 来自 [Ch_ch1][Ch_ch2]
...
```

## 数组索引计算

### Capture Buffer 索引
```c
// 第i帧，第src_ch通道的数据
int capture_index = i * capture_dev.channels + src_ch;
int32_t sample = capture_buffer[capture_index];
```

### Playback Buffer 索引
```c
// 第i帧，第j通道的数据
int playback_index = i * playback_dev.channels + j;
playback_buffer[playback_index] = sample;
```

## 当前实现的特点

### 优点
1. **灵活的通道选择**: 可以选择任意两个采集通道作为立体声输出
2. **简单直接**: 直接复制采样数据，无额外处理
3. **支持多通道播放**: 如果播放设备支持>2通道，其余通道填充0

### 限制
1. **固定为立体声**: 硬编码只处理前2个播放通道
2. **无混音功能**: 不支持多通道混音到立体声
3. **无音量控制**: 直接复制原始采样值

## 可能的改进方向

1. **支持单声道输出**: 将选定通道复制到所有播放通道
2. **支持混音**: 将多个采集通道混音到立体声
3. **音量控制**: 添加增益调节功能
4. **通道映射**: 支持更复杂的通道路由配置

## 使用示例

```bash
# 默认：采集通道0->播放左声道，采集通道1->播放右声道
./alsa_c2p_test

# 自定义：采集通道5->播放左声道，采集通道7->播放右声道
./alsa_c2p_test -c 5 -C 7

# 单声道：采集通道3->播放左右声道
./alsa_c2p_test -c 3 -C 3
```
