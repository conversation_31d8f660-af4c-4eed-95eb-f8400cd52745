#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <alsa/asoundlib.h>
#include <signal.h>
#include <unistd.h>
#include <sched.h>
// 录音参数设置
#define SAMPLE_RATE     96000   // 采样率 96kHz
#define BIT_DEPTH       32       // 位深 32位
#define CHANNELS        8        // 声道数 8
#define CHANNELS_PER_FILE 2      // 每个文件的声道数
#define NUM_OUTPUT_FILES (CHANNELS / CHANNELS_PER_FILE) // 输出文件数量
#define FRAMES_PER_PERIOD 960   // 每个周期的帧数

// PCM格式为有符号24位小端
#define FORMAT          SND_PCM_FORMAT_S32_LE

// 音频设备名称
#define AUDIO_DEVICE    "hw:1,0"  // 使用指定的音频设备

// 控制录音运行的标志
volatile sig_atomic_t running = 1;

// 信号处理函数，用于优雅退出
void handle_signal(int sig) {
    printf("\n捕获到信号 %d，正在停止录音...\n", sig);
    running = 0;
}

// 执行I2C命令
int execute_i2c_command() {
    int result = 0;
    result |= system("i2cset -y -f 2 0x40 0x08 0x20");
    result |= system("i2cset -y -f 2 0x40 0x12 0x07");
    result |= system("i2cset -y -f 2 0x40 0x00 0x71");
    result |= system("i2cset -y -f 2 0x40 0x00 0x41");

    result |= system("i2cset -y -f 2 0x43 0x08 0x20");
    result |= system("i2cset -y -f 2 0x43 0x12 0x07");
    result |= system("i2cset -y -f 2 0x43 0x00 0x71");
    result |= system("i2cset -y -f 2 0x43 0x00 0x41");

    if (result != 0) {
        fprintf(stderr, "执行I2C命令失败，错误码: %d\n", result);
        return 0;
    }
    printf("I2C命令执行成功\n");
    return 1;
}

int main(int argc, char *argv[]) {
    snd_pcm_t *capture_handle;
    snd_pcm_hw_params_t *hw_params;
    char output_filename_base[256] = "output";
    FILE *output_files[NUM_OUTPUT_FILES] = {NULL};
    int err;
    char *buffer = NULL;
    snd_pcm_uframes_t frames = FRAMES_PER_PERIOD;
    size_t buffer_size;
    
    // 处理命令行参数
    if (argc > 1) {
        strncpy(output_filename_base, argv[1], sizeof(output_filename_base) - 1);
        output_filename_base[sizeof(output_filename_base) - 1] = '\0'; // 确保字符串正确终止
    }

    struct sched_param sched_param;
    memset(&sched_param, 0, sizeof(sched_param));
    sched_param.sched_priority = 99; // 最高实时优先级
    if (sched_setscheduler(0, SCHED_FIFO, &sched_param) < 0) {
      fprintf(stderr, "无法设置实时调度器，尝试以root权限运行\n");
    }

    printf("开始录音，使用设备: %s\n", AUDIO_DEVICE);
    printf("将8个声道分组为4组双声道文件\n");
    printf("数据将保存到: %s_group[0-3].raw\n", output_filename_base);
    printf("采样率: %d Hz, 位深: %d 位, 声道数: %d\n", SAMPLE_RATE, BIT_DEPTH, CHANNELS);
    printf("按 Ctrl+C 停止录音\n");
    
    // 在录音开始前执行I2C命令
    if (!execute_i2c_command()) {
        fprintf(stderr, "无法执行所需的I2C命令，退出程序\n");
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, handle_signal);
    signal(SIGTERM, handle_signal);
    
    // 打开输出文件，为每对通道创建一个文件
    char filename[300];
    for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
        sprintf(filename, "%s_group%d.raw", output_filename_base, i);
        output_files[i] = fopen(filename, "wb");
        if (!output_files[i]) {
            fprintf(stderr, "无法打开输出文件 %s\n", filename);
            // 关闭已经打开的文件
            for (int j = 0; j < i; j++) {
                fclose(output_files[j]);
            }
            return 1;
        }
        printf("创建文件: %s (包含声道 %d 和 %d)\n", filename, i*2, i*2+1);
    }
    
    // 打开PCM设备 - 使用指定的硬件设备
    if ((err = snd_pcm_open(&capture_handle, AUDIO_DEVICE, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        fprintf(stderr, "无法打开音频设备 %s: %s\n", AUDIO_DEVICE, snd_strerror(err));
        for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
            if (output_files[i]) {
                fclose(output_files[i]);
            }
        }
        return 1;
    }
    
    // 分配hw_params结构
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构: %s\n", snd_strerror(err));
        snd_pcm_close(capture_handle);
        for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
            if (output_files[i]) {
                fclose(output_files[i]);
            }
        }
        return 1;
    }
    
    // 初始化hw_params
    if ((err = snd_pcm_hw_params_any(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 设置访问类型
    if ((err = snd_pcm_hw_params_set_access(capture_handle, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        fprintf(stderr, "无法设置访问类型: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(capture_handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 设置采样率
    unsigned int actual_rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(capture_handle, hw_params, &actual_rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    if (actual_rate != SAMPLE_RATE) {
        printf("警告: 实际采样率 (%u Hz) 与请求的不同 (%d Hz)\n", actual_rate, SAMPLE_RATE);
    }
    
    // 设置声道数
    if ((err = snd_pcm_hw_params_set_channels(capture_handle, hw_params, CHANNELS)) < 0) {
        fprintf(stderr, "无法设置声道数: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 设置周期大小
    if ((err = snd_pcm_hw_params_set_period_size_near(capture_handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 设置更大的缓冲区
    snd_pcm_uframes_t real_buffer_size = frames * 8; // 比period大几倍
    if ((err = snd_pcm_hw_params_set_buffer_size_near(capture_handle, hw_params, &real_buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        goto cleanup;
    }

    // 打印实际使用的buffer size
    snd_pcm_uframes_t actual_buffer_frames;
    snd_pcm_hw_params_get_buffer_size(hw_params, &actual_buffer_frames);
    printf("实际缓冲区大小: %lu 帧\n", actual_buffer_frames);

    // 将参数写入设备
    if ((err = snd_pcm_hw_params(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法设置参数: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 准备录音
    if ((err = snd_pcm_prepare(capture_handle)) < 0) {
        fprintf(stderr, "无法准备音频接口: %s\n", snd_strerror(err));
        goto cleanup;
    }
    
    // 计算并分配缓冲区大小
    int bytes_per_sample = BIT_DEPTH / 8;
    if (bytes_per_sample == 0) {
        fprintf(stderr, "位深计算错误，无法分配缓冲区\n");
        goto cleanup;
    }
    
    buffer_size = frames * CHANNELS * bytes_per_sample;
    buffer = (char*) malloc(buffer_size);
    if (!buffer) {
        fprintf(stderr, "无法分配缓冲区内存\n");
        goto cleanup;
    }

    while (running) {
        err = snd_pcm_readi(capture_handle, buffer, 1);
        if (err == -EPIPE) {
            fprintf(stderr, "溢出发生，准备恢复\n");
            snd_pcm_prepare(capture_handle);
            continue;
        }
        // 检查第一个采样点的通道2和通道3的bit7是否为1
        int32_t *sample_data = (int32_t *)buffer;
        
        // 获取通道2和通道3的第一个采样点
        int32_t ch2_sample = sample_data[2];  // 通道2
        int32_t ch3_sample = sample_data[3];  // 通道3
        
        // 检查bit7是否为1 (对于有符号32位整数，检查对应的位)
        if ((ch2_sample & 0x00000080) && (ch3_sample & 0x00000080)) {
            // 读取额外的3帧
            int extra_frames = SAMPLE_RATE / 48000 - 1;
            printf("检测到通道2和通道3的bit7为1，读取额外%d帧\n", extra_frames);
            snd_pcm_readi(capture_handle, buffer, extra_frames);
            break;
        }
    }
    
    //printf("录音中... (缓冲区大小: %lu 字节)\n", buffer_size);
    
    // 录音循环
    while (running) {
        err = snd_pcm_readi(capture_handle, buffer, frames);
        if (err == -EPIPE) {
            fprintf(stderr, "溢出发生，准备恢复\n");
            snd_pcm_prepare(capture_handle);
        } else if (err < 0) {
            fprintf(stderr, "从音频接口读取错误: %s\n", snd_strerror(err));
            break;
        } else if (err != (int)frames) {
            fprintf(stderr, "短读，读取 %d 帧而不是 %lu\n", err, frames);
        } else {
            // 分离并写入每组通道的数据到对应的文件
            int sample_size = bytes_per_sample * CHANNELS;
            
            // 创建临时缓冲区来存储每组通道的数据
            char *group_buffers[NUM_OUTPUT_FILES];
            for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
                // 为每个双声道文件分配缓冲区
                size_t group_buffer_size = (size_t)frames * CHANNELS_PER_FILE * bytes_per_sample;
                group_buffers[i] = (char*) malloc(group_buffer_size);
                if (!group_buffers[i]) {
                    fprintf(stderr, "无法为通道组 %d 分配缓冲区\n", i);
                    // 释放已分配的缓冲区
                    for (int j = 0; j < i; j++) {
                        free(group_buffers[j]);
                    }
                    goto cleanup;
                }
                
                // 清空缓冲区以避免垃圾数据
                memset(group_buffers[i], 0, group_buffer_size);
            }
            
            // 将交错的数据分离到各个通道组
            for (unsigned int frame = 0; frame < frames; frame++) {
                for (int group = 0; group < NUM_OUTPUT_FILES; group++) {
                    for (int ch_offset = 0; ch_offset < CHANNELS_PER_FILE; ch_offset++) {
                        int channel = group * CHANNELS_PER_FILE + ch_offset;
                        
                        // 安全检查
                        if (channel >= CHANNELS) {
                            fprintf(stderr, "内部错误: 通道索引超出范围\n");
                            continue;
                        }
                        
                        // 源数据位置 (在8通道交错数据中)
                        size_t src_offset = frame * sample_size + channel * bytes_per_sample;
                        
                        // 目标数据位置 (在双声道数据中)
                        size_t dst_offset = frame * CHANNELS_PER_FILE * bytes_per_sample + ch_offset * bytes_per_sample;
                        
                        // 边界检查
                        if (src_offset + bytes_per_sample > buffer_size) {
                            fprintf(stderr, "警告: 源缓冲区越界访问已阻止\n");
                            continue;
                        }
                        
                        size_t group_buffer_size = (size_t)frames * CHANNELS_PER_FILE * bytes_per_sample;
                        if (dst_offset + bytes_per_sample > group_buffer_size) {
                            fprintf(stderr, "警告: 目标缓冲区越界访问已阻止\n");
                            continue;
                        }
                        
                        // 复制每个采样点到对应通道组的缓冲区
                        memcpy(group_buffers[group] + dst_offset, 
                               buffer + src_offset, 
                               bytes_per_sample);
                    }
                }
            }
            
            // 写入每个通道组的数据到对应文件
            for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
                size_t data_size = frames * CHANNELS_PER_FILE * bytes_per_sample;
                size_t written = fwrite(group_buffers[i], 1, data_size, output_files[i]);
                if (written != data_size) {
                    fprintf(stderr, "警告: 文件写入不完整, 写入: %zu / %zu 字节\n", written, data_size);
                }
                free(group_buffers[i]);
                group_buffers[i] = NULL;
            }
            
            // 显示进度指示
            fputc('.', stdout);
            fflush(stdout);
        }
    }
    
    printf("\n录音完成，数据已保存到4个双声道文件: %s_group[0-3].raw\n", output_filename_base);
    
    // 清理
    if (buffer) {
        free(buffer);
        buffer = NULL;
    }
    
cleanup:
    if (hw_params) {
        snd_pcm_hw_params_free(hw_params);
    }
    if (capture_handle) {
        snd_pcm_close(capture_handle);
    }
    for (int i = 0; i < NUM_OUTPUT_FILES; i++) {
        if (output_files[i]) {
            fclose(output_files[i]);
            output_files[i] = NULL;
        }
    }
    
    return 0;
}