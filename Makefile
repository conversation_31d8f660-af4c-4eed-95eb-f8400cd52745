# 录音程序Makefile - 支持交叉编译

# 可配置变量
# 默认本地编译工具链
CROSS ?= 1
CC ?= gcc
# 交叉编译工具链前缀(例如: arm-linux-gnueabihf-, aarch64-linux-gnu-)
CROSS_COMPILE ?= /home/<USER>/work/rk3308/ym_rk_audio/buildroot/output/rockchip_rk3308_b_release/host/bin/aarch64-linux-
CROSS_COMPILE ?= /home/<USER>/work/rk3576/rk3576_ym_audio/buildroot/output/rockchip_rk3576/host/bin/aarch64-linux-

# 目标文件名
TARGETS = audio_test alsa_loopback alsa_c2p_test

# 编译选项
CFLAGS = -Wall -Wextra -O2
LDFLAGS = -lasound

# 源文件
AUDIO_TEST_SRC = audio_test.c
ALSA_LOOPBACK_SRC = alsa_loopback.c
ALSA_C2P_TEST_SRC = alsa_c2p_test.c

# 交叉编译配置
ifdef CROSS
	CC = $(CROSS_COMPILE)gcc
    # 可根据目标平台添加额外的编译标志
    # 例如: CFLAGS += -march=armv7-a
endif

# 默认目标
all: $(TARGETS)

# 编译目标
audio_test: $(AUDIO_TEST_SRC)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

alsa_loopback: $(ALSA_LOOPBACK_SRC)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

alsa_c2p_test: $(ALSA_C2P_TEST_SRC)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# 清理目标
clean:
	rm -f $(TARGETS) *.o

# 安装目标(可选)
install: $(TARGETS)
	install -d $(DESTDIR)/usr/local/bin
	install -m 755 $(TARGETS) $(DESTDIR)/usr/local/bin

# 帮助目标
help:
	@echo "可用的make目标:"
	@echo "  all        - 编译程序(默认)"
	@echo "  clean      - 删除编译生成的文件"
	@echo "  install    - 安装程序到系统目录"
	@echo ""
	@echo "交叉编译示例:"
	@echo "  make CROSS=1 CROSS_COMPILE=arm-linux-gnueabihf-"
	@echo "  make CROSS=1 CROSS_COMPILE=aarch64-linux-gnu-"

.PHONY: all clean install help