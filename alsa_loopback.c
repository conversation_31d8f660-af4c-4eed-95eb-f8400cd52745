/**
 * @file alsa_loopback.c
 * @brief ALSA audio loopback test program.
 *
 * This program captures audio from one sound card, converts the sample format,
 * and plays it back on another sound card.
 *
 * - Capture Device: hw:4,0 (48kHz, S24_LE, 2 channels)
 * - Playback Device: hw:1,0 (48kHz, S32_LE, 2 channels)
 * - Sets high real-time priority (SCHED_FIFO) to prevent dropouts.
 * - Frame length is configurable via command-line argument (-t <ms>).
 *
 * Compilation:
 * gcc -o alsa_loopback alsa_loopback.c -lasound -lrt
 *
 * Usage (requires root privileges for setting real-time priority):
 * sudo ./alsa_loopback -t 4  // Use a 4ms frame length
 */

#include <alsa/asoundlib.h>
#include <sched.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// --- 配置常量 ---
#define CAPTURE_DEVICE "hw:4,0"
#define PLAYBACK_DEVICE "hw:1,0"
#define NUM_CHANNELS 2
#define SAMPLE_RATE 48000
#define CAPTURE_FORMAT SND_PCM_FORMAT_S24_LE
#define PLAYBACK_FORMAT SND_PCM_FORMAT_S32_LE

// 24位样本占3个字节
#define BYTES_PER_S24_SAMPLE 3
// 32位样本占4个字节
#define BYTES_PER_S32_SAMPLE 4

/**
 * @brief 设置ALSA PCM设备参数
 *
 * @param pcm_handle ALSA PCM句柄指针
 * @param device_name 设备名称 (e.g., "hw:1,0")
 * @param stream 流类型 (CAPTURE or PLAYBACK)
 * @param format PCM格式
 * @param channels 声道数
 * @param rate 采样率
 * @param period_frames_ptr 周期帧大小的指针 (输入期望值, 输出实际值)
 * @return 0 on success, negative error code on failure.
 */
int setup_alsa_device(snd_pcm_t **pcm_handle, const char *device_name, snd_pcm_stream_t stream, snd_pcm_format_t format,
                      unsigned int channels, unsigned int *rate, snd_pcm_uframes_t *period_frames_ptr)
{
    snd_pcm_hw_params_t *hw_params;
    int err;

    // 打开PCM设备
    if ((err = snd_pcm_open(pcm_handle, device_name, stream, 0)) < 0)
    {
        fprintf(stderr, "无法打开PCM设备 %s: %s\n", device_name, snd_strerror(err));
        return err;
    }

    // 分配硬件参数结构
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0)
    {
        fprintf(stderr, "无法分配硬件参数结构: %s\n", snd_strerror(err));
        return err;
    }

    // 使用设备支持的所有配置填充硬件参数结构
    if ((err = snd_pcm_hw_params_any(*pcm_handle, hw_params)) < 0)
    {
        fprintf(stderr, "无法初始化硬件参数结构: %s\n", snd_strerror(err));
        goto cleanup_hw_params;
    }

    // 设置交错模式
    if ((err = snd_pcm_hw_params_set_access(*pcm_handle, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED)) < 0)
    {
        fprintf(stderr, "无法设置访问类型: %s\n", snd_strerror(err));
        goto cleanup_hw_params;
    }

    // 设置样本格式
    if ((err = snd_pcm_hw_params_set_format(*pcm_handle, hw_params, format)) < 0)
    {
        fprintf(stderr, "无法设置样本格式 %s: %s\n", snd_pcm_format_name(format), snd_strerror(err));
        goto cleanup_hw_params;
    }

    // 设置采样率
    unsigned int current_rate = *rate;
    if ((err = snd_pcm_hw_params_set_rate_near(*pcm_handle, hw_params, &current_rate, 0)) < 0)
    {
        fprintf(stderr, "无法设置采样率 %u: %s\n", *rate, snd_strerror(err));
        goto cleanup_hw_params;
    }
    if (current_rate != *rate)
    {
        fprintf(stderr, "采样率不完全匹配. 请求: %u, 得到: %u\n", *rate, current_rate);
    }

    // 设置声道数
    if ((err = snd_pcm_hw_params_set_channels(*pcm_handle, hw_params, channels)) < 0)
    {
        fprintf(stderr, "无法设置声道数 %u: %s\n", channels, snd_strerror(err));
        goto cleanup_hw_params;
    }

    // 设置周期大小（帧）
    int dir = 0;
    if ((err = snd_pcm_hw_params_set_period_size_near(*pcm_handle, hw_params, period_frames_ptr, &dir)) < 0)
    {
        fprintf(stderr, "无法设置周期大小 %lu 帧: %s\n", *period_frames_ptr, snd_strerror(err));
        goto cleanup_hw_params;
    }

    // 将参数写入驱动
    if ((err = snd_pcm_hw_params(*pcm_handle, hw_params)) < 0)
    {
        fprintf(stderr, "无法设置硬件参数: %s\n", snd_strerror(err));
        goto cleanup_hw_params;
    }

    printf("ALSA设备 '%s' 配置成功:\n", device_name);
    printf("  -> 格式: %s, 声道: %u, 采样率: %u Hz\n", snd_pcm_format_name(format), channels, *rate);
    printf("  -> 周期大小: %lu 帧\n", *period_frames_ptr);

cleanup_hw_params:
    snd_pcm_hw_params_free(hw_params);
    return err;
}

/**
 * @brief 将S24_LE格式的音频数据转换为S32_LE格式
 *
 * @param in_buf 输入缓冲区 (S24_LE, 3字节/样本)
 * @param out_buf 输出缓冲区 (S32_LE, 4字节/样本)
 * @param frames 要转换的帧数
 */
void convert_s24le_to_s32le(const uint8_t *in_buf, int32_t *out_buf, long frames)
{
    long samples = frames * NUM_CHANNELS;
    for (long i = 0; i < samples; ++i)
    {
        // 从3字节的小端格式构建一个32位整数
        int32_t val = (in_buf[0]) | (in_buf[1] << 8) | (in_buf[2] << 16);

        // 如果24位的最高位（符号位）是1，则进行符号扩展到32位
        if (val & 0x00800000)
        {
            val |= 0xFF000000;
        }

        out_buf[i] = val;
        in_buf += BYTES_PER_S24_SAMPLE;
    }
}

int main(int argc, char *argv[])
{
    snd_pcm_t *capture_handle = NULL;
    snd_pcm_t *playback_handle = NULL;
    uint8_t *capture_buffer = NULL;
    int32_t *playback_buffer = NULL;
    int err;
    int frame_duration_ms = 4; // 默认帧长为4ms

    // --- 解析命令行参数 ---
    if (argc > 1 && strcmp(argv[1], "-t") == 0 && argc > 2)
    {
        frame_duration_ms = atoi(argv[2]);
        if (frame_duration_ms != 1 && frame_duration_ms != 4 && frame_duration_ms != 8)
        {
            fprintf(stderr, "无效的帧长: %s. 请使用 1, 4, 或 8.\n", argv[2]);
            return 1;
        }
    }
    else
    {
        printf("用法: %s -t <ms>\n", argv[0]);
        printf("  <ms> : 帧长, 可选值为 1, 4, 或 8. (默认: 4ms)\n");
    }
    printf("使用 %dms 帧长进行测试.\n", frame_duration_ms);

    // --- 设置实时优先级 ---
    struct sched_param param;
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1)
    {
        perror("sched_setscheduler失败, 请尝试使用sudo运行");
        // 不退出，可能在没有实时权限的情况下也能工作
    }

    // --- 计算并设置ALSA参数 ---
    unsigned int rate = SAMPLE_RATE;
    snd_pcm_uframes_t period_frames = (snd_pcm_uframes_t)(rate * frame_duration_ms / 1000.0);

    // --- 设置采集设备 ---
    if (setup_alsa_device(&capture_handle, CAPTURE_DEVICE, SND_PCM_STREAM_CAPTURE, CAPTURE_FORMAT, NUM_CHANNELS, &rate, &period_frames) < 0)
    {
        return 1;
    }

    // --- 设置播放设备 ---
    // 确保两个设备的周期大小完全相同
    if (setup_alsa_device(&playback_handle, PLAYBACK_DEVICE, SND_PCM_STREAM_PLAYBACK, PLAYBACK_FORMAT, NUM_CHANNELS, &rate,
                          &period_frames) < 0)
    {
        goto cleanup;
    }

    // --- 分配缓冲区 ---
    size_t capture_buffer_size = period_frames * NUM_CHANNELS * BYTES_PER_S24_SAMPLE;
    capture_buffer = malloc(capture_buffer_size);
    if (!capture_buffer)
    {
        fprintf(stderr, "无法为采集分配缓冲区\n");
        goto cleanup;
    }

    size_t playback_buffer_size = period_frames * NUM_CHANNELS * BYTES_PER_S32_SAMPLE;
    playback_buffer = malloc(playback_buffer_size);
    if (!playback_buffer)
    {
        fprintf(stderr, "无法为播放分配缓冲区\n");
        goto cleanup;
    }

    printf("开始音频回环... 按 Ctrl+C 停止.\n");

    // --- 主循环 ---
    while (1)
    {
        // 从采集设备读取数据
        err = snd_pcm_readi(capture_handle, capture_buffer, period_frames);
        if (err == -EPIPE)
        {
            // XRUN (overrun)
            fprintf(stderr, "采集发生overrun!\n");
            snd_pcm_prepare(capture_handle);
            continue;
        }
        else if (err < 0)
        {
            fprintf(stderr, "从采集设备读取错误: %s\n", snd_strerror(err));
            break;
        }
        else if (err != (int)period_frames)
        {
            fprintf(stderr, "短读: 读取 %d 帧, 期望 %lu 帧\n", err, period_frames);
        }

        long frames_read = err;

        // 转换样本格式
        convert_s24le_to_s32le(capture_buffer, playback_buffer, frames_read);

        // 向播放设备写入数据
        err = snd_pcm_writei(playback_handle, playback_buffer, frames_read);
        if (err == -EPIPE)
        {
            // XRUN (underrun)
            fprintf(stderr, "播放发生underrun!\n");
            snd_pcm_prepare(playback_handle);
        }
        else if (err < 0)
        {
            fprintf(stderr, "向播放设备写入错误: %s\n", snd_strerror(err));
            break;
        }
        else if (err != frames_read)
        {
            fprintf(stderr, "短写: 写入 %d 帧, 期望 %ld 帧\n", err, frames_read);
        }
    }

cleanup:
    printf("清理并退出...\n");
    if (capture_buffer)
        free(capture_buffer);
    if (playback_buffer)
        free(playback_buffer);
    if (capture_handle)
        snd_pcm_close(capture_handle);
    if (playback_handle)
        snd_pcm_close(playback_handle);
    return 0;
}
